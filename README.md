## 1. Install Core Dependencies

```bash
bash# React + TypeScript
pnpm add react react-dom
pnpm add -D @types/react @types/react-dom typescript

# Vite (build tool)
pnpm add -D vite @vitejs/plugin-react

# Electron
pnpm add -D electron
```

## 2. Install Database & Analytics

```bash
# Database
pnpm add better-sqlite3
pnpm add -D @types/better-sqlite3

# Charts & Analytics
pnpm add recharts

# Date utilities
pnpm add date-fns
```

## 3. Install UI & Styling

```bash
# Tailwind CSS
pnpm add -D tailwindcss @tailwindcss/vite

# Icons
pnpm add lucide-react

# Utility classes
pnpm add clsx class-variance-authority
```

```ts
import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [
    tailwindcss(),
  ],
})
```

```css
@import "tailwindcss";
```

## 4. Install Development Tools

```bash
# ESLint
pnpm add -D eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser
pnpm add -D eslint-plugin-react-hooks eslint-plugin-react-refresh

# Development helpers
pnpm add -D concurrently wait-on

# Electron integration
pnpm add -D vite-plugin-electron

# Build tool
pnpm add -D electron-builder
```

## 5. Add Scripts to package.json
Scripts section:

```json
"dev": "concurrently \"vite\" \"wait-on http://localhost:5173 && electron .\"",
"build": "tsc && vite build",
"build:electron": "npm run build && electron-builder"
```

## TODO

- Database Integration: Connect to your actual voter database
- Import/Export: CSV import/export functionality
- Advanced Filters: Filter by age, gender, status, etc.
- Bulk Operations: Select multiple voters for bulk actions
- Voting History: Track voting participation over time
- Reports: Generate voter statistics and reports
- Print Features: Print voter lists or individual cards

So far I have been working with dummy data for the Polling station, section and voter data trying to get the ui right. I think I have a decent setup and now I would like to use real data. Please check src databse schema.sql to setup a better-sqlite3 databse. When everything is done tell me how can I import data from my csv.

A good option would be to create a menu where it says <h1 class="font-semibold">All Voters</h1> and allows me to import csv, and also export csv and save as pdf file.

So what voter details to display in the main area?
- These are the csv import headers: name,relation_type,relation_name,house_number,birth_year,gender,epic_number,polling_station,section

so for the main table i want you to import data sequentially according to the csv and just add a column Serial, Name, Age (covert year to age), Gender, Epic, Voter Status Default active (you will know what this is according to the schema). In the current implemention for each voter there is a three dots and and dropdown menu retain it we will make use of it later.

I guess this is a good start to implement real data. Implement when you are at least 90% sure of the required task if not feel free to clarify.