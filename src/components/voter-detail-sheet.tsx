"use client"

import * as React from "react"
import {
  CalendarDays,
  Mail,
  MapPin,
  Phone,
  User,
  FileText,
  Calendar,
  Edit3,
  Save,
  X,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Voter } from "@/types/voter"

interface VoterDetailSheetProps {
  voter: Voter | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function VoterDetailSheet({ voter, open, onOpenChange }: VoterDetailSheetProps) {
  const [isEditing, setIsEditing] = React.useState(false)
  const [editedVoter, setEditedVoter] = React.useState<Voter | null>(null)

  React.useEffect(() => {
    if (voter) {
      setEditedVoter({ ...voter })
      setIsEditing(false)
    }
  }, [voter])

  if (!voter || !editedVoter) return null

  const handleSave = () => {
    // Here you would typically save to your backend
    console.log("Saving voter:", editedVoter)
    setIsEditing(false)
    // You could call an onSave callback here
  }

  const handleCancel = () => {
    setEditedVoter({ ...voter })
    setIsEditing(false)
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
        <SheetHeader className="space-y-4 pb-6">
          <div className="space-y-1">
            <SheetTitle className="flex items-center gap-3 text-xl">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                <User className="h-5 w-5 text-primary" />
              </div>
              {voter.fullName}
            </SheetTitle>
            <SheetDescription className="text-sm text-muted-foreground">
              Voter #{voter.voterNumber} • Station {voter.pollingStationId},
              Section {voter.sectionId}
            </SheetDescription>
          </div>

          {/* Enhanced Status Section with Edit Button */}
          <div className="flex items-center justify-between rounded-lg bg-muted/30 p-3">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <UserCheck className="h-4 w-4 text-muted-foreground" />
                <Badge
                  variant={
                    voter.status === "Active"
                      ? "default"
                      : voter.status === "Inactive"
                      ? "secondary"
                      : voter.status === "Moved"
                      ? "outline"
                      : "destructive"
                  }
                  className="font-medium"
                >
                  {voter.status}
                </Badge>
              </div>
              {voter.lastVoted && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  Last voted: {new Date(voter.lastVoted).toLocaleDateString()}
                </div>
              )}
            </div>
            <Button
              variant={isEditing ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
              className="h-8 px-3 transition-all duration-200 ease-linear"
            >
              {isEditing ? (
                <>
                  <X className="mr-1 h-3 w-3" />
                  Cancel
                </>
              ) : (
                <>
                  <Edit3 className="mr-1 h-3 w-3" />
                  Edit
                </>
              )}
            </Button>
          </div>
        </SheetHeader>

        <div className="space-y-8 px-4 py-2">
          {/* Personal Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 pb-2">
              <User className="h-4 w-4 text-primary" />
              <h3 className="text-base font-semibold text-foreground">
                Personal Information
              </h3>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label
                  htmlFor="firstName"
                  className="text-sm font-medium text-muted-foreground"
                >
                  First Name
                </Label>
                <div className="group transition-all duration-200 ease-linear">
                  {isEditing ? (
                    <Input
                      id="firstName"
                      value={editedVoter.firstName}
                      onChange={(e) =>
                        setEditedVoter({
                          ...editedVoter,
                          firstName: e.target.value,
                        })
                      }
                      className="transition-all duration-200 ease-linear focus:ring-2 focus:ring-primary/20"
                    />
                  ) : (
                    <div className="flex items-center gap-2 h-9 px-3 py-1 bg-muted/50 rounded-md border border-input text-sm transition-all duration-200 ease-linear group-hover:bg-muted/60">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>{voter.firstName}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <Label
                  htmlFor="lastName"
                  className="text-sm font-medium text-muted-foreground"
                >
                  Last Name
                </Label>
                <div className="group transition-all duration-200 ease-linear">
                  {isEditing ? (
                    <Input
                      id="lastName"
                      value={editedVoter.lastName}
                      onChange={(e) =>
                        setEditedVoter({
                          ...editedVoter,
                          lastName: e.target.value,
                        })
                      }
                      className="transition-all duration-200 ease-linear focus:ring-2 focus:ring-primary/20"
                    />
                  ) : (
                    <div className="flex items-center gap-2 h-9 px-3 py-1 bg-muted/50 rounded-md border border-input text-sm transition-all duration-200 ease-linear group-hover:bg-muted/60">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>{voter.lastName}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dateOfBirths" className="text-muted-foreground">Date of Birth</Label>
                {isEditing ? (
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={editedVoter.dateOfBirth}
                    onChange={(e) =>
                      setEditedVoter({
                        ...editedVoter,
                        dateOfBirth: e.target.value,
                      })
                    }
                  />
                ) : (
                  <div className="flex items-center gap-2 h-9 px-3 py-1 bg-muted/50 rounded-md border border-input text-sm">
                    <CalendarDays className="h-4 w-4 text-muted-foreground" />
                    {new Date(voter.dateOfBirth).toLocaleDateString()} (Age{" "}
                    {voter.age})
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender" className="text-muted-foreground">Gender</Label>
                {isEditing ? (
                  <Select
                    value={editedVoter.gender}
                    onValueChange={(value) =>
                      setEditedVoter({
                        ...editedVoter,
                        gender: value as "Male" | "Female" | "Other",
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Male">Male</SelectItem>
                      <SelectItem value="Female">Female</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="flex items-center gap-2 h-9 px-3 py-1 bg-muted/50 rounded-md border border-input text-sm">
                    <User className="h-4 w-4 text-muted-foreground" />
                    {voter.gender}
                  </div>
                )}
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Contact Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 pb-2">
              <MapPin className="h-4 w-4 text-primary" />
              <h3 className="text-base font-semibold text-foreground">
                Contact Information
              </h3>
            </div>

            <div className="space-y-3">
              <Label
                htmlFor="address"
                className="text-sm font-medium text-muted-foreground"
              >
                Address
              </Label>
              <div className="group transition-all duration-200 ease-linear">
                {isEditing ? (
                  <Textarea
                    id="address"
                    value={editedVoter.address}
                    onChange={(e) =>
                      setEditedVoter({
                        ...editedVoter,
                        address: e.target.value,
                      })
                    }
                    className="min-h-[80px] transition-all duration-200 ease-linear focus:ring-2 focus:ring-primary/20"
                  />
                ) : (
                  <div className="flex items-start gap-2 min-h-[80px] px-3 py-2 bg-muted/50 rounded-md border border-input text-sm transition-all duration-200 ease-linear group-hover:bg-muted/60">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                    <span className="leading-relaxed">{voter.address}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phoneNumber" className="text-muted-foreground">Phone Number</Label>
                {isEditing ? (
                  <Input
                    id="phoneNumber"
                    value={editedVoter.phoneNumber || ""}
                    onChange={(e) =>
                      setEditedVoter({
                        ...editedVoter,
                        phoneNumber: e.target.value,
                      })
                    }
                  />
                ) : (
                  <div className="flex items-center gap-2 h-9 px-3 py-1 bg-muted/50 rounded-md border border-input text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    {voter.phoneNumber || "Not provided"}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-muted-foreground">Email</Label>
                {isEditing ? (
                  <Input
                    id="email"
                    type="email"
                    value={editedVoter.email || ""}
                    onChange={(e) =>
                      setEditedVoter({ ...editedVoter, email: e.target.value })
                    }
                  />
                ) : (
                  <div className="flex items-center gap-2 h-9 px-3 py-1 bg-muted/50 rounded-md border border-input text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    {voter.email || "Not provided"}
                  </div>
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* Registration Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Registration Information</h3>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-muted-foreground">Registration Date</Label>
                <div className="flex items-center gap-2 h-9 px-3 py-1 bg-muted/50 rounded-md border border-input text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  {new Date(voter.registrationDate).toLocaleDateString()}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status" className="text-muted-foreground">Status</Label>
                {isEditing ? (
                  <Select
                    value={editedVoter.status}
                    onValueChange={(value) =>
                      setEditedVoter({
                        ...editedVoter,
                        status: value as
                          | "Active"
                          | "Inactive"
                          | "Moved"
                          | "Deceased",
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                      <SelectItem value="Moved">Moved</SelectItem>
                      <SelectItem value="Deceased">Deceased</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="flex items-center gap-2 h-9 px-3 py-1 bg-muted/50 rounded-md border border-input text-sm">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    {voter.status}
                  </div>
                )}
              </div>
            </div>

            {voter.notes && (
              <div className="space-y-2">
                <Label htmlFor="notes" className="text-muted-foreground">Notes</Label>
                {isEditing ? (
                  <Textarea
                    id="notes"
                    value={editedVoter.notes || ""}
                    onChange={(e) =>
                      setEditedVoter({ ...editedVoter, notes: e.target.value })
                    }
                  />
                ) : (
                  <div className="flex items-start gap-2 min-h-[80px] px-3 py-2 bg-muted/50 rounded-md border border-input text-sm">
                    <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                    <span className="leading-relaxed">{voter.notes}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {isEditing && (
          <SheetFooter className="border-t bg-muted/20 px-6 py-4 mt-8">
            <div className="flex w-full gap-3">
              <Button
                variant="outline"
                onClick={handleCancel}
                className="flex-1 transition-all duration-200 ease-linear hover:bg-muted"
                size="lg"
              >
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="flex-1 transition-all duration-200 ease-linear"
                size="lg"
              >
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </div>
          </SheetFooter>
        )}
      </SheetContent>
    </Sheet>
  );
}
