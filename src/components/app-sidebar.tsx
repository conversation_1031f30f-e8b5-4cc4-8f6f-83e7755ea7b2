import * as React from "react"
import { ChevronDown, MapPin, Users } from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"



// Sample data structure for polling stations
const pollingStations = [
  {
    id: 1,
    name: "Station 001 - Central School",
    voterCount: 1245,
    sections: [
      { id: 1, name: "Section A", voterCount: 312 },
      { id: 2, name: "Section B", voterCount: 298 },
      { id: 3, name: "Section C", voterCount: 335 },
      { id: 4, name: "Section D", voterCount: 300 },
    ],
  },
  {
    id: 2,
    name: "Station 002 - Community Center",
    voterCount: 892,
    sections: [
      { id: 1, name: "Section A", voterCount: 445 },
      { id: 2, name: "Section B", voterCount: 447 },
    ],
  },
  {
    id: 3,
    name: "Station 003 - Town Hall",
    voterCount: 1567,
    sections: [
      { id: 1, name: "Section A", voterCount: 234 },
      { id: 2, name: "Section B", voterCount: 267 },
      { id: 3, name: "Section C", voterCount: 289 },
      { id: 4, name: "Section D", voterCount: 298 },
      { id: 5, name: "Section E", voterCount: 245 },
      { id: 6, name: "Section F", voterCount: 234 },
    ],
  },
  // Add more stations as needed...
]

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  onSectionSelect?: (section: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  }) => void
  selectedSection?: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  } | null
}

export function AppSidebar({ onSectionSelect, selectedSection, ...props }: AppSidebarProps) {
  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <div className="flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm h-12">
            <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
              <span className="truncate text-lg">8</span>
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium">MAWHATI</span>
              <span className="truncate text-xs text-muted-foreground">Voter Management</span>
            </div>
          </div>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Polling Stations</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {pollingStations.map((station) => (
                <Collapsible key={station.id} defaultOpen className="group/collapsible">
                  <SidebarMenuItem className="flex items-center">
                    {/* Station name button: select all voters in station */}
                    <SidebarMenuButton
                      className="flex-1"
                      onClick={() =>
                        onSectionSelect?.({
                          stationId: station.id,
                          sectionId: null,
                          stationName: station.name,
                          sectionName: null,
                        })
                      }
                      aria-selected={selectedSection?.stationId === station.id && selectedSection?.sectionId == null}
                    >
                      <MapPin className="size-4" />
                      <span className="truncate max-w-32 flex-1 text-left">{station.name}</span>
                      <span className="text-xs text-muted-foreground">{station.voterCount}</span>
                    </SidebarMenuButton>
                    {/* Chevron only toggles expand/collapse */}
                    <CollapsibleTrigger asChild>
                      <button type="button" className="ml-2">
                        <ChevronDown className="size-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
                      </button>
                    </CollapsibleTrigger>
                  </SidebarMenuItem>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {station.sections.map((section) => {
                        const isSelected = selectedSection?.stationId === station.id && selectedSection?.sectionId === section.id
                        return (
                          <SidebarMenuSubItem key={section.id}>
                            <SidebarMenuSubButton asChild>
                              <button
                                className={`w-full flex items-center gap-2 text-left transition-colors ${
                                  isSelected
                                    ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                                    : 'hover:bg-sidebar-accent/50'
                                }`}
                                onClick={() =>
                                  onSectionSelect?.({
                                    stationId: station.id,
                                    sectionId: section.id,
                                    stationName: station.name,
                                    sectionName: section.name,
                                  })
                                }
                              >
                                <Users className="size-3" />
                                <span>{section.name}</span>
                                <span className="ml-auto text-xs text-muted-foreground">{section.voterCount}</span>
                              </button>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        )
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
