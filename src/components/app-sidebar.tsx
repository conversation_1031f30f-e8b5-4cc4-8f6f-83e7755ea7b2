import * as React from "react"
import { <PERSON><PERSON>ronDown, MapPin, Users } from "lucide-react"

import {
  <PERSON><PERSON>,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import ElectronDatabaseService from "@/database/electronDatabaseService"
import { PollingStation, Section } from "@/types/voter"



// Interface for sidebar polling station data
interface SidebarPollingStation {
  id: number
  name: string
  voter_count: number
  sections: SidebarSection[]
}

interface SidebarSection {
  id: number
  name: string
  voter_count: number
}

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  onSectionSelect?: (section: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  }) => void
  selectedSection?: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  } | null
}

export function AppSidebar({ onSectionSelect, selectedSection, ...props }: AppSidebarProps) {
  const [pollingStations, setPollingStations] = React.useState<SidebarPollingStation[]>([])
  const [loading, setLoading] = React.useState(true)

  // Load polling stations and sections from database
  React.useEffect(() => {
    loadPollingStations()
  }, [])

  const loadPollingStations = async () => {
    try {
      setLoading(true)
      const db = ElectronDatabaseService.getInstance()

      // Get all voters to determine which sections belong to which polling stations
      const voters = await db.getAllVoters()

      console.log('Debug: Total voters loaded:', voters.length)
      console.log('Debug: Sample voter:', voters[0])

      // Create a map of polling station -> sections based on actual voter data
      const stationSectionMap = new Map<number, Set<number>>()
      const sectionVoterCounts = new Map<number, number>()
      const stationVoterCounts = new Map<number, number>()

      // Process voters to build relationships
      voters.forEach((voter: any) => {
        console.log('Debug: Processing voter:', {
          name: voter.name,
          polling_station_id: voter.polling_station_id,
          section_id: voter.section_id,
          polling_station_name: voter.polling_station_name,
          section_name: voter.section_name
        })

        if (voter.polling_station_id && voter.section_id) {
          // Track which sections belong to which stations
          if (!stationSectionMap.has(voter.polling_station_id)) {
            stationSectionMap.set(voter.polling_station_id, new Set())
          }
          stationSectionMap.get(voter.polling_station_id)!.add(voter.section_id)

          // Count voters per section
          sectionVoterCounts.set(voter.section_id, (sectionVoterCounts.get(voter.section_id) || 0) + 1)

          // Count voters per station
          stationVoterCounts.set(voter.polling_station_id, (stationVoterCounts.get(voter.polling_station_id) || 0) + 1)
        }
      })

      console.log('Debug: Station-Section Map:', Array.from(stationSectionMap.entries()))
      console.log('Debug: Section Voter Counts:', Array.from(sectionVoterCounts.entries()))
      console.log('Debug: Station Voter Counts:', Array.from(stationVoterCounts.entries()))

      // Get all polling stations and sections
      const stations = await db.getAllPollingStations()
      const sections = await db.getAllSections()

      console.log('Debug: All stations:', stations)
      console.log('Debug: All sections:', sections)

      // Build the final structure with proper relationships
      const stationsWithSections: SidebarPollingStation[] = stations.map((station: any) => {
        const stationSections = stationSectionMap.get(station.id) || new Set()

        // TEMPORARY: If no voters are loaded, show all sections under all stations for debugging
        const sectionsToShow = voters.length === 0 ? sections : sections.filter((section: any) => stationSections.has(section.id))

        const stationData = {
          id: station.id,
          name: station.name,
          voter_count: stationVoterCounts.get(station.id) || 0,
          sections: sectionsToShow.map((section: any) => ({
            id: section.id,
            name: section.name,
            voter_count: sectionVoterCounts.get(section.id) || 0
          }))
        }

        console.log('Debug: Station with sections:', stationData)
        return stationData
      })

      setPollingStations(stationsWithSections)
    } catch (error) {
      console.error('Failed to load polling stations:', error)
      // Set empty array on error
      setPollingStations([])
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Sidebar variant="inset" {...props}>
        <SidebarHeader>
          <SidebarMenu>
            <div className="flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm h-12">
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <span className="truncate text-lg">8</span>
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">MAWHATI</span>
                <span className="truncate text-xs text-muted-foreground">Loading...</span>
              </div>
            </div>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Loading Polling Stations...</SidebarGroupLabel>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    )
  }

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <div className="flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm h-12">
            <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
              <span className="truncate text-lg">8</span>
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium">MAWHATI</span>
              <span className="truncate text-xs text-muted-foreground">Voter Management</span>
            </div>
          </div>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Polling Stations</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {pollingStations.map((station) => (
                <Collapsible key={station.id} defaultOpen className="group/collapsible">
                  <SidebarMenuItem className="flex items-center">
                    {/* Station name button: select all voters in station */}
                    <SidebarMenuButton
                      className="flex-1"
                      onClick={() =>
                        onSectionSelect?.({
                          stationId: station.id,
                          sectionId: null,
                          stationName: station.name,
                          sectionName: null,
                        })
                      }
                      aria-selected={selectedSection?.stationId === station.id && selectedSection?.sectionId == null}
                    >
                      <MapPin className="size-4" />
                      <span className="truncate max-w-32 flex-1 text-left">{station.name}</span>
                      <span className="text-xs text-muted-foreground">{station.voter_count}</span>
                    </SidebarMenuButton>
                    {/* Chevron only toggles expand/collapse */}
                    <CollapsibleTrigger asChild>
                      <button type="button" className="ml-2">
                        <ChevronDown className="size-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
                      </button>
                    </CollapsibleTrigger>
                  </SidebarMenuItem>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {station.sections.map((section) => {
                        const isSelected = selectedSection?.stationId === station.id && selectedSection?.sectionId === section.id
                        return (
                          <SidebarMenuSubItem key={section.id}>
                            <SidebarMenuSubButton asChild>
                              <button
                                className={`w-full flex items-center gap-2 text-left transition-colors ${
                                  isSelected
                                    ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                                    : 'hover:bg-sidebar-accent/50'
                                }`}
                                onClick={() =>
                                  onSectionSelect?.({
                                    stationId: station.id,
                                    sectionId: section.id,
                                    stationName: station.name,
                                    sectionName: section.name,
                                  })
                                }
                              >
                                <Users className="size-3" />
                                <span>{section.name}</span>
                                <span className="ml-auto text-xs text-muted-foreground">{section.voter_count}</span>
                              </button>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        )
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
