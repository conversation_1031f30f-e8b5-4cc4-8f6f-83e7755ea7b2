import * as React from "react"
import { Upload, Download, FileText, AlertCircle, CheckCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Voter } from "@/types/voter"
import ExportService from "@/services/exportService"

interface ImportExportMenuProps {
  voters: Voter[]
  onImportComplete: () => void
}

interface ImportResult {
  success: boolean
  totalRows: number
  successfulImports: number
  errors: string[]
  duplicates: string[]
}

export function ImportExportMenu({ voters, onImportComplete }: ImportExportMenuProps) {
  const [isImportDialogOpen, setIsImportDialogOpen] = React.useState(false)
  const [importResult, setImportResult] = React.useState<ImportResult | null>(null)
  const [isImporting, setIsImporting] = React.useState(false)
  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const exportService = new ExportService()

  const handleImportClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.toLowerCase().endsWith('.csv')) {
      alert('Please select a CSV file')
      return
    }

    setIsImporting(true)
    setIsImportDialogOpen(true)

    try {
      const result = await exportService.importFromFile(file)
      setImportResult(result)
      if (result.success) {
        onImportComplete()
      }
    } catch (error) {
      setImportResult({
        success: false,
        totalRows: 0,
        successfulImports: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred'],
        duplicates: []
      })
    } finally {
      setIsImporting(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleExportCSV = () => {
    exportService.exportToCSV(voters, `voters_${new Date().toISOString().split('T')[0]}.csv`)
  }

  const handleExportPDF = () => {
    exportService.exportToPDF(voters, `voters_${new Date().toISOString().split('T')[0]}.pdf`)
  }

  const handleExportDetailedPDF = () => {
    exportService.exportDetailedToPDF(voters, `voters_detailed_${new Date().toISOString().split('T')[0]}.pdf`)
  }

  const closeImportDialog = () => {
    setIsImportDialogOpen(false)
    setImportResult(null)
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Upload className="mr-2 h-4 w-4" />
            Import/Export
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Import Data</DropdownMenuLabel>
          <DropdownMenuItem onClick={handleImportClick}>
            <Upload className="mr-2 h-4 w-4" />
            Import CSV
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>Export Data</DropdownMenuLabel>
          <DropdownMenuItem onClick={handleExportCSV}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleExportPDF}>
            <FileText className="mr-2 h-4 w-4" />
            Export PDF (Summary)
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleExportDetailedPDF}>
            <FileText className="mr-2 h-4 w-4" />
            Export PDF (Detailed)
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />

      <Dialog open={isImportDialogOpen} onOpenChange={closeImportDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>CSV Import Results</DialogTitle>
            <DialogDescription>
              {isImporting ? 'Importing voter data...' : 'Import process completed'}
            </DialogDescription>
          </DialogHeader>

          {isImporting ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3">Processing CSV file...</span>
            </div>
          ) : importResult ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                {importResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-500" />
                )}
                <span className="font-medium">
                  {importResult.success ? 'Import Successful' : 'Import Failed'}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Total Rows</p>
                  <p className="text-2xl font-bold">{importResult.totalRows}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Successfully Imported</p>
                  <p className="text-2xl font-bold text-green-600">{importResult.successfulImports}</p>
                </div>
              </div>

              {importResult.duplicates.length > 0 && (
                <div>
                  <p className="font-medium mb-2">Duplicates Found:</p>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {importResult.duplicates.map((duplicate, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {duplicate}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {importResult.errors.length > 0 && (
                <div>
                  <p className="font-medium mb-2">Errors:</p>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {importResult.errors.map((error, index) => (
                      <Badge key={index} variant="destructive" className="text-xs">
                        {error}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end">
                <Button onClick={closeImportDialog}>Close</Button>
              </div>
            </div>
          ) : null}
        </DialogContent>
      </Dialog>
    </>
  )
}

export default ImportExportMenu
