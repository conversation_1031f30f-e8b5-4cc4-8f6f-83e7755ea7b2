// Electron renderer database service that communicates with main process via IPC
export class ElectronDatabaseService {
  private static instance: ElectronDatabaseService

  private constructor() {
    // Private constructor for singleton
  }

  public static getInstance(): ElectronDatabaseService {
    if (!ElectronDatabaseService.instance) {
      ElectronDatabaseService.instance = new ElectronDatabaseService()
    }
    return ElectronDatabaseService.instance
  }

  // Check if we're running in Electron
  private get isElectron(): boolean {
    return typeof window !== 'undefined' && window.electronAPI && window.electronAPI.isElectron
  }

  // Voter CRUD operations
  public async getAllVoters() {
    if (!this.isElectron) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getAllVoters()
  }

  public async getVoterById(id: number) {
    if (!this.isElectron) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getVoterById(id)
  }

  public async getVotersByStation(stationId: number) {
    if (!this.isElectron) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getVotersByStation(stationId)
  }

  public async getVotersBySection(stationId: number, sectionId: number) {
    if (!this.isElectron) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getVotersBySection(stationId, sectionId)
  }

  // Polling station operations
  public async getAllPollingStations() {
    if (!this.isElectron) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getAllPollingStations()
  }

  public async getAllSections() {
    if (!this.isElectron) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getAllSections()
  }

  // Helper methods
  public async getOrCreatePollingStation(name: string): Promise<number> {
    if (!this.isElectron) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getOrCreatePollingStation(name)
  }

  public async getOrCreateSection(name: string): Promise<number> {
    if (!this.isElectron) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getOrCreateSection(name)
  }

  // CSV import
  public async importCSV(csvContent: string) {
    if (!this.isElectron) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.importCSV(csvContent)
  }

  // Menu event handlers
  public onMenuImportCSV(callback: () => void): () => void {
    if (!this.isElectron) {
      return () => {} // Return empty cleanup function
    }
    return window.electronAPI.onMenuImportCSV(callback)
  }

  public onMenuExportCSV(callback: () => void): () => void {
    if (!this.isElectron) {
      return () => {} // Return empty cleanup function
    }
    return window.electronAPI.onMenuExportCSV(callback)
  }

  public onMenuExportPDF(callback: () => void): () => void {
    if (!this.isElectron) {
      return () => {} // Return empty cleanup function
    }
    return window.electronAPI.onMenuExportPDF(callback)
  }
}

export default ElectronDatabaseService
