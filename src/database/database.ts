import Database from 'better-sqlite3'

export class DatabaseService {
  private db: Database.Database
  private static instance: DatabaseService

  private constructor() {
    // Create database path - handle both Electron and browser environments
    let dbPath = 'voters.db'

    try {
      // Try to use Electron's app.getPath if available
      const { app } = require('electron')
      if (app) {
        const path = require('path')
        const fs = require('fs')
        const userDataPath = app.getPath('userData')
        dbPath = path.join(userDataPath, 'voters.db')

        // Ensure directory exists
        fs.mkdirSync(userDataPath, { recursive: true })
        console.log('Database path (Electron):', dbPath)
      }
    } catch (error) {
      // Running in browser or Electron not available
      console.log('Database path (Browser):', dbPath)
    }

    this.db = new Database(dbPath)
    this.initializeDatabase()
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService()
    }
    return DatabaseService.instance
  }

  private initializeDatabase() {
    try {
      // For now, create basic tables manually since we can't read the schema file in browser
      this.createBasicSchema()

      // Create default admin user if not exists
      this.createDefaultUser()

      console.log('Database initialized successfully')
    } catch (error) {
      console.error('Failed to initialize database:', error)
      throw error
    }
  }

  private createBasicSchema() {
    // Drop existing voter table if it exists to recreate with correct constraints
    this.db.exec(`DROP TABLE IF EXISTS voter;`)

    // Create basic tables needed for the app
    this.db.exec(`
      PRAGMA foreign_keys = ON;

      CREATE TABLE IF NOT EXISTS user (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE COLLATE NOCASE,
        full_name TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('admin', 'user')) DEFAULT 'user',
        password_hash TEXT NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        last_login INTEGER,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      );

      CREATE TABLE IF NOT EXISTS polling_station (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE COLLATE NOCASE,
        is_active BOOLEAN DEFAULT 1,
        created_by INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      );

      CREATE TABLE IF NOT EXISTS section (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE COLLATE NOCASE,
        is_active BOOLEAN DEFAULT 1,
        created_by INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      );

      CREATE TABLE IF NOT EXISTS voter (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL COLLATE NOCASE,
        epic_number TEXT NOT NULL UNIQUE COLLATE NOCASE,
        house_number TEXT,
        birth_year INTEGER CHECK (birth_year >= 1900 AND birth_year <= 2030),
        gender TEXT NOT NULL CHECK (gender IN ('Male', 'Female', 'Other')),
        relation_type TEXT CHECK (relation_type IN ('Father', 'Mother', 'Husband', 'Others')),
        relation_name TEXT,
        polling_station_id INTEGER,
        section_id INTEGER,
        voter_status TEXT NOT NULL CHECK (voter_status IN ('Active', 'Expired', 'Shifted', 'Duplicate', 'Missing', 'Disqualified')) DEFAULT 'Active',
        created_by INTEGER NOT NULL DEFAULT 1,
        updated_by INTEGER,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        FOREIGN KEY (polling_station_id) REFERENCES polling_station(id),
        FOREIGN KEY (section_id) REFERENCES section(id),
        CHECK (epic_number GLOB '[A-Z][A-Z][A-Z][0-9][0-9][0-9][0-9][0-9][0-9][0-9]' AND length(epic_number) = 10)
      );

      CREATE INDEX IF NOT EXISTS idx_voter_epic_number ON voter(epic_number);
      CREATE INDEX IF NOT EXISTS idx_voter_name ON voter(name COLLATE NOCASE);
      CREATE INDEX IF NOT EXISTS idx_voter_polling_station ON voter(polling_station_id);
      CREATE INDEX IF NOT EXISTS idx_voter_section ON voter(section_id);
      CREATE INDEX IF NOT EXISTS idx_voter_status ON voter(voter_status);
    `)
  }

  private createDefaultUser() {
    const userExists = this.db.prepare('SELECT COUNT(*) as count FROM user').get() as { count: number }

    if (userExists.count === 0) {
      const insertUser = this.db.prepare(`
        INSERT INTO user (username, full_name, role, password_hash, is_active)
        VALUES (?, ?, ?, ?, ?)
      `)

      // Create default admin user (password should be hashed in production)
      insertUser.run('admin', 'Administrator', 'admin', 'admin_hash', 1)
      console.log('Default admin user created')
    }
  }

  // Voter CRUD operations
  public getAllVoters() {
    const stmt = this.db.prepare(`
      SELECT
        v.id,
        v.name,
        v.epic_number,
        v.house_number,
        v.birth_year,
        v.gender,
        v.relation_type,
        v.relation_name,
        v.voter_status,
        ps.name as polling_station_name,
        s.name as section_name,
        v.polling_station_id,
        v.section_id,
        v.created_at,
        v.updated_at
      FROM voter v
      LEFT JOIN polling_station ps ON v.polling_station_id = ps.id
      LEFT JOIN section s ON v.section_id = s.id
      WHERE v.voter_status NOT IN ('Duplicate', 'Disqualified')
      ORDER BY v.id ASC
    `)
    return stmt.all()
  }

  public getVoterById(id: number) {
    const stmt = this.db.prepare(`
      SELECT
        v.*,
        ps.name as polling_station_name,
        s.name as section_name
      FROM voter v
      LEFT JOIN polling_station ps ON v.polling_station_id = ps.id
      LEFT JOIN section s ON v.section_id = s.id
      WHERE v.id = ?
    `)
    return stmt.get(id)
  }

  public getVotersByStation(stationId: number) {
    const stmt = this.db.prepare(`
      SELECT
        v.id,
        v.name,
        v.epic_number,
        v.house_number,
        v.birth_year,
        v.gender,
        v.relation_type,
        v.relation_name,
        v.voter_status,
        ps.name as polling_station_name,
        s.name as section_name,
        v.polling_station_id,
        v.section_id
      FROM voter v
      LEFT JOIN polling_station ps ON v.polling_station_id = ps.id
      LEFT JOIN section s ON v.section_id = s.id
      WHERE v.polling_station_id = ? AND v.voter_status NOT IN ('Duplicate', 'Disqualified')
      ORDER BY v.id ASC
    `)
    return stmt.all(stationId)
  }

  public getVotersBySection(stationId: number, sectionId: number) {
    const stmt = this.db.prepare(`
      SELECT
        v.id,
        v.name,
        v.epic_number,
        v.house_number,
        v.birth_year,
        v.gender,
        v.relation_type,
        v.relation_name,
        v.voter_status,
        ps.name as polling_station_name,
        s.name as section_name,
        v.polling_station_id,
        v.section_id
      FROM voter v
      LEFT JOIN polling_station ps ON v.polling_station_id = ps.id
      LEFT JOIN section s ON v.section_id = s.id
      WHERE v.polling_station_id = ? AND v.section_id = ? AND v.voter_status NOT IN ('Duplicate', 'Disqualified')
      ORDER BY v.id ASC
    `)
    return stmt.all(stationId, sectionId)
  }

  // Polling station operations
  public getAllPollingStations() {
    const stmt = this.db.prepare(`
      SELECT
        ps.*,
        COUNT(v.id) as voter_count
      FROM polling_station ps
      LEFT JOIN voter v ON ps.id = v.polling_station_id AND v.voter_status NOT IN ('Duplicate', 'Disqualified')
      WHERE ps.is_active = 1
      GROUP BY ps.id
      ORDER BY ps.name
    `)
    return stmt.all()
  }

  public getAllSections() {
    const stmt = this.db.prepare(`
      SELECT
        s.*,
        COUNT(v.id) as voter_count
      FROM section s
      LEFT JOIN voter v ON s.id = v.section_id AND v.voter_status NOT IN ('Duplicate', 'Disqualified')
      WHERE s.is_active = 1
      GROUP BY s.id
      ORDER BY s.name
    `)
    return stmt.all()
  }

  // Helper method to get or create polling station
  public getOrCreatePollingStation(name: string): number {
    const existing = this.db.prepare('SELECT id FROM polling_station WHERE name = ? COLLATE NOCASE').get(name) as { id: number } | undefined

    if (existing) {
      return existing.id
    }

    const insert = this.db.prepare(`
      INSERT INTO polling_station (name, created_by)
      VALUES (?, 1)
    `)
    const result = insert.run(name)
    return result.lastInsertRowid as number
  }

  // Helper method to get or create section
  public getOrCreateSection(name: string): number {
    const existing = this.db.prepare('SELECT id FROM section WHERE name = ? COLLATE NOCASE').get(name) as { id: number } | undefined

    if (existing) {
      return existing.id
    }

    const insert = this.db.prepare(`
      INSERT INTO section (name, created_by)
      VALUES (?, 1)
    `)
    const result = insert.run(name)
    return result.lastInsertRowid as number
  }

  public close() {
    this.db.close()
  }

  public getDatabase() {
    return this.db
  }
}

export default DatabaseService
