-- Voter Management System Database Schema - Production Ready

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- User Management with Role-Based Access
CREATE TABLE user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE COLLATE NOCASE,
    full_name TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('admin', 'user')) DEFAULT 'user',
    password_hash TEXT NOT NULL, -- Store bcrypt hash
    is_active BOOLEAN DEFAULT 1,
    last_login INTEGER,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

CREATE INDEX idx_user_username ON user(username);
CREATE INDEX idx_user_role ON user(role);
CREATE INDEX idx_user_active ON user(is_active);

-- Location Tables
CREATE TABLE polling_station (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE COLLATE NOCASE,
    is_active BOOLEAN DEFAULT 1,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE INDEX idx_polling_station_name ON polling_station(name);
CREATE INDEX idx_polling_station_active ON polling_station(is_active);

CREATE TABLE section (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE COLLATE NOCASE,
    is_active BOOLEAN DEFAULT 1,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE INDEX idx_section_name ON section(name);
CREATE INDEX idx_section_active ON section(is_active);

-- Reference Tables with Predefined Values
CREATE TABLE education_level (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE COLLATE NOCASE,
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE TABLE occupation (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE COLLATE NOCASE,
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE TABLE community (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE COLLATE NOCASE,
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE TABLE religion (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE COLLATE NOCASE,
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE TABLE contact_type (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE COLLATE NOCASE,
    icon TEXT,
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (created_by) REFERENCES user(id)
);

-- Core Voter Table with Business Validations
CREATE TABLE voter (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL COLLATE NOCASE,
    epic_number TEXT NOT NULL UNIQUE COLLATE NOCASE,
    house_number TEXT,
    birth_year INTEGER CHECK (birth_year >= 1900 AND birth_year <= strftime('%Y', 'now')),
    gender TEXT NOT NULL CHECK (gender IN ('Male', 'Female', 'Other')),
    relation_type TEXT CHECK (relation_type IN ('Father', 'Mother', 'Husband', 'Others')),
    relation_name TEXT,
    polling_station_id INTEGER,
    section_id INTEGER,
    voter_status TEXT NOT NULL CHECK (voter_status IN ('Active', 'Expired', 'Shifted', 'Duplicate', 'Missing', 'Disqualified')) DEFAULT 'Active',
    education_level_id INTEGER,
    occupation_id INTEGER,
    community_id INTEGER,
    religion_id INTEGER,
    created_by INTEGER NOT NULL,
    updated_by INTEGER,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (polling_station_id) REFERENCES polling_station(id),
    FOREIGN KEY (section_id) REFERENCES section(id),
    FOREIGN KEY (education_level_id) REFERENCES education_level(id),
    FOREIGN KEY (occupation_id) REFERENCES occupation(id),
    FOREIGN KEY (community_id) REFERENCES community(id),
    FOREIGN KEY (religion_id) REFERENCES religion(id),
    FOREIGN KEY (created_by) REFERENCES user(id),
    FOREIGN KEY (updated_by) REFERENCES user(id),
    -- EPIC Number validation: 3 uppercase letters + 7 digits
    CHECK (epic_number GLOB '[A-Z][A-Z][A-Z][0-9][0-9][0-9][0-9][0-9][0-9][0-9]' AND length(epic_number) = 10)
);

-- FTS Virtual Table for name search
CREATE VIRTUAL TABLE voter_fts USING fts5(name, house_number, relation_name, content='voter', content_rowid='id');

-- Triggers to keep FTS table in sync
CREATE TRIGGER voter_ai AFTER INSERT ON voter BEGIN
    INSERT INTO voter_fts(rowid, name, house_number, relation_name)
    VALUES (new.id, new.name, COALESCE(new.house_number, ''), COALESCE(new.relation_name, ''));
END;

CREATE TRIGGER voter_ad AFTER DELETE ON voter BEGIN
    INSERT INTO voter_fts(voter_fts, rowid, name, house_number, relation_name)
    VALUES ('delete', old.id, old.name, COALESCE(old.house_number, ''), COALESCE(old.relation_name, ''));
END;

CREATE TRIGGER voter_au AFTER UPDATE ON voter BEGIN
    INSERT INTO voter_fts(voter_fts, rowid, name, house_number, relation_name)
    VALUES ('delete', old.id, old.name, COALESCE(old.house_number, ''), COALESCE(old.relation_name, ''));
    INSERT INTO voter_fts(rowid, name, house_number, relation_name)
    VALUES (new.id, new.name, COALESCE(new.house_number, ''), COALESCE(new.relation_name, ''));
END;

-- Comprehensive indexes for performance
CREATE INDEX idx_voter_epic_number ON voter(epic_number);
CREATE INDEX idx_voter_name ON voter(name COLLATE NOCASE);
CREATE INDEX idx_voter_birth_year ON voter(birth_year);
CREATE INDEX idx_voter_gender ON voter(gender);
CREATE INDEX idx_voter_relation_type ON voter(relation_type);
CREATE INDEX idx_voter_polling_station ON voter(polling_station_id);
CREATE INDEX idx_voter_section ON voter(section_id);
CREATE INDEX idx_voter_status ON voter(voter_status);
CREATE INDEX idx_voter_created_at ON voter(created_at);
CREATE INDEX idx_voter_updated_at ON voter(updated_at);
CREATE INDEX idx_voter_created_by ON voter(created_by);

-- Composite indexes for common queries
CREATE INDEX idx_voter_station_section ON voter(polling_station_id, section_id);
CREATE INDEX idx_voter_status_station ON voter(voter_status, polling_station_id);
CREATE INDEX idx_voter_gender_age ON voter(gender, birth_year);

-- Contact Information with Validation
CREATE TABLE voter_contact (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    voter_id INTEGER NOT NULL,
    contact_type_id INTEGER NOT NULL,
    contact_value TEXT NOT NULL,
    is_primary BOOLEAN DEFAULT 0,
    is_verified BOOLEAN DEFAULT 0,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (voter_id) REFERENCES voter(id) ON DELETE CASCADE,
    FOREIGN KEY (contact_type_id) REFERENCES contact_type(id),
    FOREIGN KEY (created_by) REFERENCES user(id),
    -- One contact record per type per voter
    UNIQUE(voter_id, contact_type_id),
    -- Flexible mobile number validation for Indian numbers
    CHECK (
        contact_type_id != (SELECT id FROM contact_type WHERE name = 'Mobile')
        OR
        (contact_value GLOB '[0-9]*' AND length(contact_value) BETWEEN 10 AND 15)
    ),
    -- Basic email validation
    CHECK (
        contact_type_id != (SELECT id FROM contact_type WHERE name = 'Email')
        OR
        contact_value LIKE '%@%.%'
    )
);

CREATE INDEX idx_voter_contact_voter_id ON voter_contact(voter_id);
CREATE INDEX idx_voter_contact_type_id ON voter_contact(contact_type_id);
CREATE INDEX idx_voter_contact_value ON voter_contact(contact_value);
CREATE INDEX idx_voter_contact_is_primary ON voter_contact(is_primary);

-- Notes
CREATE TABLE voter_note (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    voter_id INTEGER NOT NULL,
    note_text TEXT NOT NULL,
    note_type TEXT DEFAULT 'General',
    is_important BOOLEAN DEFAULT 0,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (voter_id) REFERENCES voter(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE INDEX idx_voter_note_voter_id ON voter_note(voter_id);
CREATE INDEX idx_voter_note_type ON voter_note(note_type);
CREATE INDEX idx_voter_note_is_important ON voter_note(is_important);
CREATE INDEX idx_voter_note_created_at ON voter_note(created_at);

-- Transaction Table
CREATE TABLE transaction (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    voter_id INTEGER NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    purpose TEXT NOT NULL,
    transaction_date INTEGER NOT NULL,
    created_by INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (voter_id) REFERENCES voter(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE INDEX idx_transaction_voter_id ON transaction(voter_id);
CREATE INDEX idx_transaction_date ON transaction(transaction_date);
CREATE INDEX idx_transaction_amount ON transaction(amount);
CREATE INDEX idx_transaction_created_by ON transaction(created_by);

-- Import Staging
CREATE TABLE temp_voter_import (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    relation_type TEXT,
    relation_name TEXT,
    house_number TEXT,
    birth_year INTEGER,
    gender TEXT,
    epic_number TEXT,
    polling_station TEXT,
    section TEXT,
    import_batch_id TEXT NOT NULL,
    validation_errors TEXT,
    is_processed BOOLEAN DEFAULT 0,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

CREATE INDEX idx_temp_voter_epic_number ON temp_voter_import(epic_number);
CREATE INDEX idx_temp_voter_polling_station ON temp_voter_import(polling_station);
CREATE INDEX idx_temp_voter_section ON temp_voter_import(section);
CREATE INDEX idx_temp_voter_batch_id ON temp_voter_import(import_batch_id);
CREATE INDEX idx_temp_voter_processed ON temp_voter_import(is_processed);

-- Business Logic Triggers

-- 1. Auto-delete voters with status 'Duplicate' or 'Disqualified'
CREATE TRIGGER auto_delete_invalid_voters
AFTER UPDATE OF voter_status ON voter
WHEN NEW.voter_status IN ('Duplicate', 'Disqualified')
BEGIN
    DELETE FROM voter WHERE id = NEW.id;
END;

-- 2. Update voter.updated_at on changes
CREATE TRIGGER voter_update_timestamp
AFTER UPDATE ON voter
WHEN OLD.updated_at = NEW.updated_at
BEGIN
    UPDATE voter SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

-- 3. Update reference tables updated_at on changes
CREATE TRIGGER polling_station_update_timestamp
AFTER UPDATE ON polling_station
WHEN OLD.updated_at = NEW.updated_at
BEGIN
    UPDATE polling_station SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

CREATE TRIGGER section_update_timestamp
AFTER UPDATE ON section
WHEN OLD.updated_at = NEW.updated_at
BEGIN
    UPDATE section SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

CREATE TRIGGER education_level_update_timestamp
AFTER UPDATE ON education_level
WHEN OLD.updated_at = NEW.updated_at
BEGIN
    UPDATE education_level SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

CREATE TRIGGER occupation_update_timestamp
AFTER UPDATE ON occupation
WHEN OLD.updated_at = NEW.updated_at
BEGIN
    UPDATE occupation SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

CREATE TRIGGER community_update_timestamp
AFTER UPDATE ON community
WHEN OLD.updated_at = NEW.updated_at
BEGIN
    UPDATE community SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

CREATE TRIGGER religion_update_timestamp
AFTER UPDATE ON religion
WHEN OLD.updated_at = NEW.updated_at
BEGIN
    UPDATE religion SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

CREATE TRIGGER contact_type_update_timestamp
AFTER UPDATE ON contact_type
WHEN OLD.updated_at = NEW.updated_at
BEGIN
    UPDATE contact_type SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

-- 4. Prevent duplicate EPIC numbers (case-insensitive)
CREATE TRIGGER prevent_duplicate_epic
BEFORE INSERT ON voter
WHEN EXISTS (SELECT 1 FROM voter WHERE UPPER(epic_number) = UPPER(NEW.epic_number))
BEGIN
    SELECT RAISE(ABORT, 'EPIC number already exists');
END;

CREATE TRIGGER prevent_duplicate_epic_update
BEFORE UPDATE OF epic_number ON voter
WHEN EXISTS (SELECT 1 FROM voter WHERE UPPER(epic_number) = UPPER(NEW.epic_number) AND id != NEW.id)
BEGIN
    SELECT RAISE(ABORT, 'EPIC number already exists');
END;

-- 5. Validate EPIC number format on insert/update
CREATE TRIGGER validate_epic_format
BEFORE INSERT ON voter
WHEN NEW.epic_number NOT GLOB '[A-Z][A-Z][A-Z][0-9][0-9][0-9][0-9][0-9][0-9][0-9]' OR length(NEW.epic_number) != 10
BEGIN
    SELECT RAISE(ABORT, 'Invalid EPIC number format. Must be 3 uppercase letters followed by 7 digits');
END;

-- Database Views for Common Access Patterns

-- View for user role to access voter data (excludes transactions)
CREATE VIEW voter_public AS
SELECT
    v.id, v.name, v.epic_number, v.house_number, v.birth_year, v.gender,
    v.relation_type, v.relation_name, v.voter_status,
    ps.name as polling_station_name,
    s.name as section_name,
    el.name as education_level,
    o.name as occupation,
    c.name as community,
    r.name as religion,
    v.created_at, v.updated_at
FROM voter v
LEFT JOIN polling_station ps ON v.polling_station_id = ps.id
LEFT JOIN section s ON v.section_id = s.id
LEFT JOIN education_level el ON v.education_level_id = el.id
LEFT JOIN occupation o ON v.occupation_id = o.id
LEFT JOIN community c ON v.community_id = c.id
LEFT JOIN religion r ON v.religion_id = r.id
WHERE v.voter_status NOT IN ('Duplicate', 'Disqualified');

-- View for duplicate detection
CREATE VIEW potential_duplicates AS
SELECT
    v1.id as voter1_id,
    v1.name as voter1_name,
    v1.epic_number as voter1_epic,
    v2.id as voter2_id,
    v2.name as voter2_name,
    v2.epic_number as voter2_epic,
    'Similar Name' as match_type
FROM voter v1
JOIN voter v2 ON v1.id < v2.id
WHERE (
    -- Similar names (Levenshtein distance approximation)
    (length(v1.name) - length(replace(lower(v1.name), lower(v2.name), ''))) > 0.7 * length(v1.name)
    OR
    -- Same house number and similar birth year
    (v1.house_number = v2.house_number AND abs(v1.birth_year - v2.birth_year) <= 2)
)
AND v1.voter_status = 'Active' AND v2.voter_status = 'Active';

-- Database Configuration for Performance
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
PRAGMA mmap_size = 268435456; -- 256MB

-- Initial Reference Data
INSERT INTO education_level (name, display_order, created_by) VALUES
('Primary', 1, 1),
('Secondary', 2, 1),
('Higher Secondary', 3, 1),
('Graduate', 4, 1),
('Post Graduate', 5, 1),
('Doctorate', 6, 1),
('Others', 7, 1);

INSERT INTO religion (name, display_order, created_by) VALUES
('Christian', 1, 1),
('Hindu', 2, 1),
('Muslim', 3, 1),
('Niam Khasi', 4, 1),
('Songsarek', 5, 1),
('Sikh', 6, 1),
('Buddhist', 7, 1),
('Jain', 8, 1),
('Other', 9, 1);

INSERT INTO community (name, display_order, created_by) VALUES
('ST', 1, 1),
('SC', 2, 1),
('OBC', 3, 1),
('Minority', 4, 1),
('General', 5, 1);

INSERT INTO occupation (name, display_order, created_by) VALUES
('Student', 1, 1),
('Government Employee', 2, 1),
('Private Employee', 3, 1),
('Business/Self Employed', 4, 1),
('Farmer', 5, 1),
('Labor/Worker', 6, 1),
('Retired', 7, 1),
('Homemaker', 8, 1),
('Unemployed', 9, 1);

INSERT INTO contact_type (name, display_order, created_by) VALUES
('Mobile', 1, 1),
('Email', 2, 1),
('WhatsApp', 3, 1),
('Facebook', 4, 1),
('Instagram', 5, 1),
('Twitter', 6, 1);