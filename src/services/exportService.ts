import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'
import { Voter } from '@/types/voter'
import ElectronCSVImportService from './electronCsvService'

export class ExportService {
  private csvService: ElectronCSVImportService

  constructor() {
    this.csvService = new ElectronCSVImportService()
  }

  public exportToCSV(voters: Voter[], filename: string = 'voters.csv'): void {
    const csvContent = this.csvService.exportToCSV(voters)
    this.downloadFile(csvContent, filename, 'text/csv')
  }

  public exportToPDF(voters: Voter[], filename: string = 'voters.pdf'): void {
    const doc = new jsPDF()

    // Add title
    doc.setFontSize(16)
    doc.text('Voter List', 14, 22)

    // Add metadata
    doc.setFontSize(10)
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 30)
    doc.text(`Total Voters: ${voters.length}`, 14, 36)

    // Prepare table data
    const tableData = voters.map((voter, index) => [
      (index + 1).toString(), // Serial number
      voter.name,
      voter.age.toString(),
      voter.gender,
      voter.epic_number,
      voter.voter_status,
      voter.polling_station_name || '',
      voter.section_name || ''
    ])

    // Add table
    autoTable(doc, {
      head: [['Serial', 'Name', 'Age', 'Gender', 'EPIC', 'Status', 'Station', 'Section']],
      body: tableData,
      startY: 45,
      styles: {
        fontSize: 8,
        cellPadding: 2,
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245]
      },
      columnStyles: {
        0: { halign: 'center', cellWidth: 15 }, // Serial
        1: { cellWidth: 35 }, // Name
        2: { halign: 'center', cellWidth: 15 }, // Age
        3: { halign: 'center', cellWidth: 20 }, // Gender
        4: { halign: 'center', cellWidth: 25 }, // EPIC
        5: { halign: 'center', cellWidth: 20 }, // Status
        6: { cellWidth: 30 }, // Station
        7: { cellWidth: 25 }, // Section
      },
      margin: { top: 45, right: 14, bottom: 20, left: 14 },
      didDrawPage: (data) => {
        // Add page numbers
        const pageCount = doc.getNumberOfPages()
        const pageSize = doc.internal.pageSize
        const pageHeight = pageSize.height || pageSize.getHeight()

        doc.setFontSize(8)
        doc.text(
          `Page ${data.pageNumber} of ${pageCount}`,
          data.settings.margin.left,
          pageHeight - 10
        )
      }
    })

    // Save the PDF
    doc.save(filename)
  }

  public exportDetailedToPDF(voters: Voter[], filename: string = 'voters_detailed.pdf'): void {
    const doc = new jsPDF()

    // Add title
    doc.setFontSize(16)
    doc.text('Detailed Voter List', 14, 22)

    // Add metadata
    doc.setFontSize(10)
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 30)
    doc.text(`Total Voters: ${voters.length}`, 14, 36)

    // Prepare detailed table data
    const tableData = voters.map((voter, index) => [
      (index + 1).toString(),
      voter.name,
      voter.age.toString(),
      voter.gender,
      voter.epic_number,
      voter.house_number || '',
      voter.relation_type || '',
      voter.relation_name || '',
      voter.voter_status,
      voter.polling_station_name || '',
      voter.section_name || ''
    ])

    // Add table with more columns
    autoTable(doc, {
      head: [['#', 'Name', 'Age', 'Gender', 'EPIC', 'House', 'Relation', 'Relation Name', 'Status', 'Station', 'Section']],
      body: tableData,
      startY: 45,
      styles: {
        fontSize: 7,
        cellPadding: 1.5,
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245]
      },
      columnStyles: {
        0: { halign: 'center', cellWidth: 10 }, // Serial
        1: { cellWidth: 25 }, // Name
        2: { halign: 'center', cellWidth: 12 }, // Age
        3: { halign: 'center', cellWidth: 15 }, // Gender
        4: { halign: 'center', cellWidth: 22 }, // EPIC
        5: { halign: 'center', cellWidth: 15 }, // House
        6: { halign: 'center', cellWidth: 18 }, // Relation
        7: { cellWidth: 20 }, // Relation Name
        8: { halign: 'center', cellWidth: 15 }, // Status
        9: { cellWidth: 25 }, // Station
        10: { cellWidth: 20 }, // Section
      },
      margin: { top: 45, right: 14, bottom: 20, left: 14 },
      didDrawPage: (data) => {
        // Add page numbers
        const pageCount = doc.getNumberOfPages()
        const pageSize = doc.internal.pageSize
        const pageHeight = pageSize.height || pageSize.getHeight()

        doc.setFontSize(8)
        doc.text(
          `Page ${data.pageNumber} of ${pageCount}`,
          data.settings.margin.left,
          pageHeight - 10
        )
      }
    })

    // Save the PDF
    doc.save(filename)
  }

  private downloadFile(content: string, filename: string, mimeType: string): void {
    const blob = new Blob([content], { type: mimeType })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  public async importFromFile(file: File): Promise<any> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const content = e.target?.result as string
          const result = await this.csvService.importFromCSV(content)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsText(file)
    })
  }
}

export default ExportService
