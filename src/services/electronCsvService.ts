import { CSVVoterData } from '@/types/voter'
import ElectronDatabaseService from '@/database/electronDatabaseService'

export interface ImportResult {
  success: boolean
  totalRows: number
  successfulImports: number
  errors: string[]
  duplicates: string[]
}

export class ElectronCSVImportService {
  private db: ElectronDatabaseService

  constructor() {
    this.db = ElectronDatabaseService.getInstance()
  }

  public async importFromCSV(csvContent: string): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      totalRows: 0,
      successfulImports: 0,
      errors: [],
      duplicates: []
    }

    try {
      const rows = this.parseCSV(csvContent)
      result.totalRows = rows.length

      if (rows.length === 0) {
        result.errors.push('No data found in CSV file')
        return result
      }

      // Validate headers
      const expectedHeaders = ['name', 'relation_type', 'relation_name', 'house_number', 'birth_year', 'gender', 'epic_number', 'polling_station', 'section']
      const headers = Object.keys(rows[0])
      
      const missingHeaders = expectedHeaders.filter(header => !headers.includes(header))
      if (missingHeaders.length > 0) {
        result.errors.push(`Missing required headers: ${missingHeaders.join(', ')}`)
        return result
      }

      // Use the main process CSV import which has access to the database
      const importResult = await this.db.importCSV(csvContent)
      return importResult

    } catch (error) {
      result.errors.push(`CSV parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  private parseCSV(csvContent: string): Record<string, any>[] {
    const lines = csvContent.trim().split('\n')
    if (lines.length < 2) {
      throw new Error('CSV must contain at least a header row and one data row')
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const rows: Record<string, any>[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i])
      if (values.length !== headers.length) {
        throw new Error(`Row ${i + 1}: Expected ${headers.length} columns, got ${values.length}`)
      }

      const row: Record<string, any> = {}
      headers.forEach((header, index) => {
        row[header] = values[index]
      })
      rows.push(row)
    }

    return rows
  }

  private parseCSVLine(line: string): string[] {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }

  public exportToCSV(voters: any[]): string {
    if (voters.length === 0) {
      return 'name,relation_type,relation_name,house_number,birth_year,gender,epic_number,polling_station,section\n'
    }

    const headers = ['name', 'relation_type', 'relation_name', 'house_number', 'birth_year', 'gender', 'epic_number', 'polling_station', 'section']
    const csvRows = [headers.join(',')]

    voters.forEach(voter => {
      const row = [
        this.escapeCSVField(voter.name || ''),
        this.escapeCSVField(voter.relation_type || ''),
        this.escapeCSVField(voter.relation_name || ''),
        this.escapeCSVField(voter.house_number || ''),
        voter.birth_year || '',
        this.escapeCSVField(voter.gender || ''),
        this.escapeCSVField(voter.epic_number || ''),
        this.escapeCSVField(voter.polling_station_name || ''),
        this.escapeCSVField(voter.section_name || '')
      ]
      csvRows.push(row.join(','))
    })

    return csvRows.join('\n')
  }

  private escapeCSVField(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`
    }
    return field
  }
}

export default ElectronCSVImportService
