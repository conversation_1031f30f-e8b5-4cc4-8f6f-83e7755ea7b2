import { CSVVoterData } from '@/types/voter'
import DatabaseService from '@/database/database'

export interface ImportResult {
  success: boolean
  totalRows: number
  successfulImports: number
  errors: string[]
  duplicates: string[]
}

export class CSVImportService {
  private db: DatabaseService

  constructor() {
    this.db = DatabaseService.getInstance()
  }

  public async importFromCSV(csvContent: string): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      totalRows: 0,
      successfulImports: 0,
      errors: [],
      duplicates: []
    }

    try {
      const rows = this.parseCSV(csvContent)
      result.totalRows = rows.length

      if (rows.length === 0) {
        result.errors.push('No data found in CSV file')
        return result
      }

      // Validate headers
      const expectedHeaders = ['name', 'relation_type', 'relation_name', 'house_number', 'birth_year', 'gender', 'epic_number', 'polling_station', 'section']
      const headers = Object.keys(rows[0])
      
      const missingHeaders = expectedHeaders.filter(header => !headers.includes(header))
      if (missingHeaders.length > 0) {
        result.errors.push(`Missing required headers: ${missingHeaders.join(', ')}`)
        return result
      }

      // Process each row
      for (let i = 0; i < rows.length; i++) {
        try {
          const rowData = rows[i] as CSVVoterData
          await this.importVoterRow(rowData, i + 2, result) // +2 because row 1 is headers, and we're 0-indexed
        } catch (error) {
          result.errors.push(`Row ${i + 2}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }

      result.success = result.successfulImports > 0
      return result

    } catch (error) {
      result.errors.push(`CSV parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  private parseCSV(csvContent: string): Record<string, any>[] {
    const lines = csvContent.trim().split('\n')
    if (lines.length < 2) {
      throw new Error('CSV must contain at least a header row and one data row')
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const rows: Record<string, any>[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i])
      if (values.length !== headers.length) {
        throw new Error(`Row ${i + 1}: Expected ${headers.length} columns, got ${values.length}`)
      }

      const row: Record<string, any> = {}
      headers.forEach((header, index) => {
        row[header] = values[index]
      })
      rows.push(row)
    }

    return rows
  }

  private parseCSVLine(line: string): string[] {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }

  private async importVoterRow(rowData: CSVVoterData, rowNumber: number, result: ImportResult): Promise<void> {
    // Validate required fields
    if (!rowData.name || !rowData.epic_number || !rowData.gender) {
      throw new Error('Missing required fields: name, epic_number, or gender')
    }

    // Validate EPIC number format
    const epicRegex = /^[A-Z]{3}[0-9]{7}$/
    if (!epicRegex.test(rowData.epic_number.toUpperCase())) {
      throw new Error(`Invalid EPIC number format: ${rowData.epic_number}. Must be 3 letters followed by 7 digits`)
    }

    // Validate gender
    const validGenders = ['Male', 'Female', 'Other']
    if (!validGenders.includes(rowData.gender)) {
      throw new Error(`Invalid gender: ${rowData.gender}. Must be Male, Female, or Other`)
    }

    // Validate birth year
    const currentYear = new Date().getFullYear()
    const birthYear = parseInt(rowData.birth_year.toString())
    if (isNaN(birthYear) || birthYear < 1900 || birthYear > currentYear) {
      throw new Error(`Invalid birth year: ${rowData.birth_year}. Must be between 1900 and ${currentYear}`)
    }

    // Validate relation type if provided
    if (rowData.relation_type) {
      const validRelations = ['Father', 'Mother', 'Husband', 'Others']
      if (!validRelations.includes(rowData.relation_type)) {
        throw new Error(`Invalid relation type: ${rowData.relation_type}. Must be Father, Mother, Husband, or Others`)
      }
    }

    // Check for duplicate EPIC number
    const existingVoter = this.db.getDatabase().prepare('SELECT epic_number FROM voter WHERE UPPER(epic_number) = UPPER(?)').get(rowData.epic_number)
    if (existingVoter) {
      result.duplicates.push(`Row ${rowNumber}: EPIC ${rowData.epic_number} already exists`)
      return
    }

    // Get or create polling station and section
    const pollingStationId = rowData.polling_station ? this.db.getOrCreatePollingStation(rowData.polling_station) : null
    const sectionId = rowData.section ? this.db.getOrCreateSection(rowData.section) : null

    // Insert voter
    const insertVoter = this.db.getDatabase().prepare(`
      INSERT INTO voter (
        name, epic_number, house_number, birth_year, gender,
        relation_type, relation_name, polling_station_id, section_id,
        voter_status, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'Active', 1)
    `)

    insertVoter.run(
      rowData.name.trim(),
      rowData.epic_number.toUpperCase(),
      rowData.house_number?.trim() || null,
      birthYear,
      rowData.gender,
      rowData.relation_type?.trim() || null,
      rowData.relation_name?.trim() || null,
      pollingStationId,
      sectionId
    )

    result.successfulImports++
  }

  public exportToCSV(voters: any[]): string {
    if (voters.length === 0) {
      return 'name,relation_type,relation_name,house_number,birth_year,gender,epic_number,polling_station,section\n'
    }

    const headers = ['name', 'relation_type', 'relation_name', 'house_number', 'birth_year', 'gender', 'epic_number', 'polling_station', 'section']
    const csvRows = [headers.join(',')]

    voters.forEach(voter => {
      const row = [
        this.escapeCSVField(voter.name || ''),
        this.escapeCSVField(voter.relation_type || ''),
        this.escapeCSVField(voter.relation_name || ''),
        this.escapeCSVField(voter.house_number || ''),
        voter.birth_year || '',
        this.escapeCSVField(voter.gender || ''),
        this.escapeCSVField(voter.epic_number || ''),
        this.escapeCSVField(voter.polling_station_name || ''),
        this.escapeCSVField(voter.section_name || '')
      ]
      csvRows.push(row.join(','))
    })

    return csvRows.join('\n')
  }

  private escapeCSVField(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`
    }
    return field
  }
}

export default CSVImportService
