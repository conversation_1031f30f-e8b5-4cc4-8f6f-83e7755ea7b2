export interface Voter {
  id: string
  voterNumber: string
  firstName: string
  lastName: string
  fullName: string
  dateOfBirth: string
  age: number
  gender: "Male" | "Female" | "Other"
  address: string
  phoneNumber?: string
  email?: string
  pollingStationId: number
  sectionId: number
  registrationDate: string
  status: "Active" | "Inactive" | "Moved" | "Deceased"
  lastVoted?: string
  notes?: string
}

export interface PollingStation {
  id: number
  name: string
  voterCount: number
  sections: Section[]
}

export interface Section {
  id: number
  name: string
  voterCount: number
}

// Sample voter data
export const sampleVoters: Voter[] = [
  {
    id: "1",
    voterNumber: "001-A-001",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    fullName: "<PERSON>",
    dateOfBirth: "1985-03-15",
    age: 39,
    gender: "Male",
    address: "123 Main Street, Downtown",
    phoneNumber: "******-0123",
    email: "<EMAIL>",
    pollingStationId: 1,
    sectionId: 1,
    registrationDate: "2020-01-15",
    status: "Active",
    lastVoted: "2023-11-07",
    notes: "Reliable voter, always participates"
  },
  {
    id: "2",
    voterNumber: "001-A-002",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    fullName: "<PERSON>",
    dateOfBirth: "1992-07-22",
    age: 32,
    gender: "Female",
    address: "456 Oak Avenue, Downtown",
    phoneNumber: "******-0124",
    email: "<EMAIL>",
    pollingStationId: 1,
    sectionId: 1,
    registrationDate: "2021-03-10",
    status: "Active",
    lastVoted: "2023-11-07"
  },
  {
    id: "3",
    voterNumber: "001-A-003",
    firstName: "Michael",
    lastName: "Brown",
    fullName: "Michael Brown",
    dateOfBirth: "1978-11-08",
    age: 46,
    gender: "Male",
    address: "789 Pine Street, Downtown",
    phoneNumber: "******-0125",
    pollingStationId: 1,
    sectionId: 1,
    registrationDate: "2019-05-20",
    status: "Active",
    lastVoted: "2023-11-07"
  },
  {
    id: "4",
    voterNumber: "001-A-004",
    firstName: "Emily",
    lastName: "Davis",
    fullName: "Emily Davis",
    dateOfBirth: "1995-01-30",
    age: 29,
    gender: "Female",
    address: "321 Elm Street, Downtown",
    phoneNumber: "******-0126",
    email: "<EMAIL>",
    pollingStationId: 1,
    sectionId: 1,
    registrationDate: "2022-08-15",
    status: "Active"
  },
  {
    id: "5",
    voterNumber: "001-A-005",
    firstName: "Robert",
    lastName: "Wilson",
    fullName: "Robert Wilson",
    dateOfBirth: "1965-09-12",
    age: 59,
    gender: "Male",
    address: "654 Maple Drive, Downtown",
    phoneNumber: "******-0127",
    pollingStationId: 1,
    sectionId: 1,
    registrationDate: "2018-02-28",
    status: "Active",
    lastVoted: "2023-11-07",
    notes: "Senior citizen, needs assistance"
  }
]
