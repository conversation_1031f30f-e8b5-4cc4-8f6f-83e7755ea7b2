// Database-aligned voter interface
export interface Voter {
  id: number
  name: string
  epic_number: string
  house_number?: string
  birth_year: number
  age: number // calculated field
  gender: "Male" | "Female" | "Other"
  relation_type?: "Father" | "Mother" | "Husband" | "Others"
  relation_name?: string
  polling_station_id?: number
  section_id?: number
  polling_station_name?: string
  section_name?: string
  voter_status: "Active" | "Expired" | "Shifted" | "Duplicate" | "Missing" | "Disqualified"
  created_at: number
  updated_at: number
}

// CSV import interface matching the headers
export interface CSVVoterData {
  name: string
  relation_type: string
  relation_name: string
  house_number: string
  birth_year: number
  gender: string
  epic_number: string
  polling_station: string
  section: string
}

export interface PollingStation {
  id: number
  name: string
  voter_count: number
  is_active: boolean
  created_at: number
  updated_at: number
}

export interface Section {
  id: number
  name: string
  voter_count: number
  is_active: boolean
  created_at: number
  updated_at: number
}

// Utility functions
export function calculateAge(birthYear: number): number {
  const currentYear = new Date().getFullYear()
  return currentYear - birthYear
}

export function formatVoterForTable(dbVoter: any): Voter {
  return {
    ...dbVoter,
    age: calculateAge(dbVoter.birth_year)
  }
}
