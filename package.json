{"name": "electixir-app", "version": "1.0.0", "main": "dist-electron/main.js", "type": "module", "scripts": {"dev": "vite", "dev:browser": "vite", "dev:electron": "vite --config vite.electron.config.ts", "build": "tsc && vite build", "build:electron": "npm run build && electron-builder"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.524.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.6.0", "electron": "^37.1.0", "electron-builder": "^26.0.12", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^7.0.0", "vite-plugin-electron": "^0.29.0"}}