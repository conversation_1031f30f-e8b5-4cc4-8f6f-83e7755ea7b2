const { contextBridge, ipc<PERSON>enderer } = require('electron')

// Expose database API to renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // Voter operations
  getAllVoters: () => ipcRenderer.invoke('db:getAllVoters'),
  getVoterById: (id: number) => ipcRenderer.invoke('db:getVoterById', id),
  getVotersByStation: (stationId: number) => ipcRenderer.invoke('db:getVotersByStation', stationId),
  getVotersBySection: (stationId: number, sectionId: number) => ipcRenderer.invoke('db:getVotersBySection', stationId, sectionId),

  // Polling station and section operations
  getAllPollingStations: () => ipcRenderer.invoke('db:getAllPollingStations'),
  getAllSections: () => ipcRenderer.invoke('db:getAllSections'),
  getOrCreatePollingStation: (name: string) => ipc<PERSON>enderer.invoke('db:getOrCreatePollingStation', name),
  getOrCreateSection: (name: string) => ipcRenderer.invoke('db:getOrCreateSection', name),

  // CSV import
  importCSV: (csvContent: string) => ipcRenderer.invoke('db:importCSV', csvContent),

  // Menu events
  onMenuImportCSV: (callback: () => void) => {
    ipcRenderer.on('menu-import-csv', callback)
    return () => ipcRenderer.removeListener('menu-import-csv', callback)
  },
  onMenuExportCSV: (callback: () => void) => {
    ipcRenderer.on('menu-export-csv', callback)
    return () => ipcRenderer.removeListener('menu-export-csv', callback)
  },
  onMenuExportPDF: (callback: () => void) => {
    ipcRenderer.on('menu-export-pdf', callback)
    return () => ipcRenderer.removeListener('menu-export-pdf', callback)
  },

  // Utility
  isElectron: true
})

// Type definitions for the exposed API
declare global {
  interface Window {
    electronAPI: {
      getAllVoters: () => Promise<any[]>
      getVoterById: (id: number) => Promise<any>
      getVotersByStation: (stationId: number) => Promise<any[]>
      getVotersBySection: (stationId: number, sectionId: number) => Promise<any[]>
      getAllPollingStations: () => Promise<any[]>
      getAllSections: () => Promise<any[]>
      getOrCreatePollingStation: (name: string) => Promise<number>
      getOrCreateSection: (name: string) => Promise<number>
      importCSV: (csvContent: string) => Promise<any>
      onMenuImportCSV: (callback: () => void) => () => void
      onMenuExportCSV: (callback: () => void) => () => void
      onMenuExportPDF: (callback: () => void) => () => void
      isElectron: boolean
    }
  }
}
