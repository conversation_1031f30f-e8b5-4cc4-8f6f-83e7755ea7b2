import { app, BrowserWindow, <PERSON>u, ipc<PERSON>ain } from 'electron'
import * as path from 'path'
import { fileURLToPath } from 'url'

// Handle __dirname in ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Import database service for main process
import DatabaseService from '../src/database/database'

// CSV Import interfaces and functions
interface ImportResult {
  success: boolean
  totalRows: number
  successfulImports: number
  errors: string[]
  duplicates: string[]
}

interface CSVVoterData {
  name: string
  relation_type: string
  relation_name: string
  house_number: string
  birth_year: number
  gender: string
  epic_number: string
  polling_station: string
  section: string
}

const createWindow = (): void => {
  const mainWindow = new BrowserWindow({
    height: 900,
    width: 1400,
    minHeight: 600,
    minWidth: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
    },
    icon: path.join(__dirname, '../public/icon.png'), // Add app icon if you have one
    titleBarStyle: 'default',
    show: false, // Don't show until ready
  })

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
  })

  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:5173')
    // Open dev tools in development for debugging
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // Create application menu
  createMenu()
}

const createMenu = (): void => {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Import CSV',
          accelerator: 'CmdOrCtrl+I',
          click: () => {
            // This will be handled by the renderer process
            BrowserWindow.getFocusedWindow()?.webContents.send('menu-import-csv')
          }
        },
        {
          label: 'Export CSV',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            BrowserWindow.getFocusedWindow()?.webContents.send('menu-export-csv')
          }
        },
        {
          label: 'Export PDF',
          accelerator: 'CmdOrCtrl+P',
          click: () => {
            BrowserWindow.getFocusedWindow()?.webContents.send('menu-export-pdf')
          }
        },
        { type: 'separator' },
        {
          label: 'Quit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ]

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    })

    // Window menu
    template[4].submenu = [
      { role: 'close' },
      { role: 'minimize' },
      { role: 'zoom' },
      { type: 'separator' },
      { role: 'front' }
    ]
  }

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

app.whenReady().then(createWindow)

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Initialize database when app is ready
let db: DatabaseService

app.whenReady().then(() => {
  createWindow()

  // Initialize database in main process
  db = DatabaseService.getInstance()

  // Set up IPC handlers for database operations
  setupDatabaseIPC()
})

// Database IPC handlers
function setupDatabaseIPC() {
  // Get all voters
  ipcMain.handle('db:getAllVoters', async () => {
    try {
      return db.getAllVoters()
    } catch (error) {
      console.error('Error getting all voters:', error)
      throw error
    }
  })

  // Get voter by ID
  ipcMain.handle('db:getVoterById', async (event, id: number) => {
    try {
      return db.getVoterById(id)
    } catch (error) {
      console.error('Error getting voter by ID:', error)
      throw error
    }
  })

  // Get voters by station
  ipcMain.handle('db:getVotersByStation', async (event, stationId: number) => {
    try {
      return db.getVotersByStation(stationId)
    } catch (error) {
      console.error('Error getting voters by station:', error)
      throw error
    }
  })

  // Get voters by section
  ipcMain.handle('db:getVotersBySection', async (event, stationId: number, sectionId: number) => {
    try {
      return db.getVotersBySection(stationId, sectionId)
    } catch (error) {
      console.error('Error getting voters by section:', error)
      throw error
    }
  })

  // Get all polling stations
  ipcMain.handle('db:getAllPollingStations', async () => {
    try {
      return db.getAllPollingStations()
    } catch (error) {
      console.error('Error getting polling stations:', error)
      throw error
    }
  })

  // Get all sections
  ipcMain.handle('db:getAllSections', async () => {
    try {
      return db.getAllSections()
    } catch (error) {
      console.error('Error getting sections:', error)
      throw error
    }
  })

  // Import CSV data
  ipcMain.handle('db:importCSV', async (event, csvContent: string) => {
    try {
      // Implement CSV import directly in main process
      return await importCSVData(csvContent)
    } catch (error) {
      console.error('Error importing CSV:', error)
      throw error
    }
  })

  // Get or create polling station
  ipcMain.handle('db:getOrCreatePollingStation', async (event, name: string) => {
    try {
      return db.getOrCreatePollingStation(name)
    } catch (error) {
      console.error('Error getting/creating polling station:', error)
      throw error
    }
  })

  // Get or create section
  ipcMain.handle('db:getOrCreateSection', async (event, name: string) => {
    try {
      return db.getOrCreateSection(name)
    } catch (error) {
      console.error('Error getting/creating section:', error)
      throw error
    }
  })
}

// CSV Import implementation for main process
async function importCSVData(csvContent: string): Promise<ImportResult> {
  const result: ImportResult = {
    success: false,
    totalRows: 0,
    successfulImports: 0,
    errors: [],
    duplicates: []
  }

  try {
    const rows = parseCSV(csvContent)
    result.totalRows = rows.length

    if (rows.length === 0) {
      result.errors.push('No data found in CSV file')
      return result
    }

    // Validate headers
    const expectedHeaders = ['name', 'relation_type', 'relation_name', 'house_number', 'birth_year', 'gender', 'epic_number', 'polling_station', 'section']
    const headers = Object.keys(rows[0])

    const missingHeaders = expectedHeaders.filter(header => !headers.includes(header))
    if (missingHeaders.length > 0) {
      result.errors.push(`Missing required headers: ${missingHeaders.join(', ')}`)
      return result
    }

    // Process each row
    for (let i = 0; i < rows.length; i++) {
      try {
        const rowData = rows[i] as CSVVoterData
        await importVoterRow(rowData, i + 2, result) // +2 because row 1 is headers, and we're 0-indexed
      } catch (error) {
        result.errors.push(`Row ${i + 2}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    result.success = result.successfulImports > 0
    return result

  } catch (error) {
    result.errors.push(`CSV parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    return result
  }
}

function parseCSV(csvContent: string): Record<string, any>[] {
  const lines = csvContent.trim().split('\n')
  if (lines.length < 2) {
    throw new Error('CSV must contain at least a header row and one data row')
  }

  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
  const rows: Record<string, any>[] = []

  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i])
    if (values.length !== headers.length) {
      throw new Error(`Row ${i + 1}: Expected ${headers.length} columns, got ${values.length}`)
    }

    const row: Record<string, any> = {}
    headers.forEach((header, index) => {
      row[header] = values[index]
    })
    rows.push(row)
  }

  return rows
}

function parseCSVLine(line: string): string[] {
  const result: string[] = []
  let current = ''
  let inQuotes = false

  for (let i = 0; i < line.length; i++) {
    const char = line[i]

    if (char === '"') {
      inQuotes = !inQuotes
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim())
      current = ''
    } else {
      current += char
    }
  }

  result.push(current.trim())
  return result
}

async function importVoterRow(rowData: CSVVoterData, rowNumber: number, result: ImportResult): Promise<void> {
  // Validate required fields
  if (!rowData.name || !rowData.epic_number || !rowData.gender) {
    throw new Error('Missing required fields: name, epic_number, or gender')
  }

  // Validate EPIC number format
  const epicRegex = /^[A-Z]{3}[0-9]{7}$/
  if (!epicRegex.test(rowData.epic_number.toUpperCase())) {
    throw new Error(`Invalid EPIC number format: ${rowData.epic_number}. Must be 3 letters followed by 7 digits`)
  }

  // Validate gender
  const validGenders = ['Male', 'Female', 'Other']
  if (!validGenders.includes(rowData.gender)) {
    throw new Error(`Invalid gender: ${rowData.gender}. Must be Male, Female, or Other`)
  }

  // Validate birth year
  const currentYear = new Date().getFullYear()
  const birthYear = parseInt(rowData.birth_year.toString())
  if (isNaN(birthYear) || birthYear < 1900 || birthYear > currentYear) {
    throw new Error(`Invalid birth year: ${rowData.birth_year}. Must be between 1900 and ${currentYear}`)
  }

  // Validate relation type if provided
  if (rowData.relation_type) {
    const validRelations = ['Father', 'Mother', 'Husband', 'Others']
    if (!validRelations.includes(rowData.relation_type)) {
      throw new Error(`Invalid relation type: ${rowData.relation_type}. Must be Father, Mother, Husband, or Others`)
    }
  }

  // Check for duplicate EPIC number
  const existingVoter = db.getDatabase().prepare('SELECT epic_number FROM voter WHERE UPPER(epic_number) = UPPER(?)').get(rowData.epic_number)
  if (existingVoter) {
    result.duplicates.push(`Row ${rowNumber}: EPIC ${rowData.epic_number} already exists`)
    return
  }

  // Get or create polling station and section
  const pollingStationId = rowData.polling_station ? db.getOrCreatePollingStation(rowData.polling_station) : null
  const sectionId = rowData.section ? db.getOrCreateSection(rowData.section) : null

  // Insert voter
  const insertVoter = db.getDatabase().prepare(`
    INSERT INTO voter (
      name, epic_number, house_number, birth_year, gender,
      relation_type, relation_name, polling_station_id, section_id,
      voter_status, created_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'Active', 1)
  `)

  insertVoter.run(
    rowData.name.trim(),
    rowData.epic_number.toUpperCase(),
    rowData.house_number?.trim() || null,
    birthYear,
    rowData.gender,
    rowData.relation_type?.trim() || null,
    rowData.relation_name?.trim() || null,
    pollingStationId,
    sectionId
  )

  result.successfulImports++
}