import { app, BrowserWindow, Menu, ipc<PERSON>ain } from 'electron'
import * as path from 'path'
import { fileURLToPath } from 'url'

// Handle __dirname in ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Import database service for main process
import DatabaseService from '../src/database/database'

const createWindow = (): void => {
  const mainWindow = new BrowserWindow({
    height: 900,
    width: 1400,
    minHeight: 600,
    minWidth: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
    },
    icon: path.join(__dirname, '../public/icon.png'), // Add app icon if you have one
    titleBarStyle: 'default',
    show: false, // Don't show until ready
  })

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
  })

  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:5173')
    // Open dev tools in development for debugging
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // Create application menu
  createMenu()
}

const createMenu = (): void => {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Import CSV',
          accelerator: 'CmdOrCtrl+I',
          click: () => {
            // This will be handled by the renderer process
            BrowserWindow.getFocusedWindow()?.webContents.send('menu-import-csv')
          }
        },
        {
          label: 'Export CSV',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            BrowserWindow.getFocusedWindow()?.webContents.send('menu-export-csv')
          }
        },
        {
          label: 'Export PDF',
          accelerator: 'CmdOrCtrl+P',
          click: () => {
            BrowserWindow.getFocusedWindow()?.webContents.send('menu-export-pdf')
          }
        },
        { type: 'separator' },
        {
          label: 'Quit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ]

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    })

    // Window menu
    template[4].submenu = [
      { role: 'close' },
      { role: 'minimize' },
      { role: 'zoom' },
      { type: 'separator' },
      { role: 'front' }
    ]
  }

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

app.whenReady().then(createWindow)

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Initialize database when app is ready
let db: DatabaseService

app.whenReady().then(() => {
  createWindow()

  // Initialize database in main process
  db = DatabaseService.getInstance()

  // Set up IPC handlers for database operations
  setupDatabaseIPC()
})

// Database IPC handlers
function setupDatabaseIPC() {
  // Get all voters
  ipcMain.handle('db:getAllVoters', async () => {
    try {
      return db.getAllVoters()
    } catch (error) {
      console.error('Error getting all voters:', error)
      throw error
    }
  })

  // Get voter by ID
  ipcMain.handle('db:getVoterById', async (event, id: number) => {
    try {
      return db.getVoterById(id)
    } catch (error) {
      console.error('Error getting voter by ID:', error)
      throw error
    }
  })

  // Get voters by station
  ipcMain.handle('db:getVotersByStation', async (event, stationId: number) => {
    try {
      return db.getVotersByStation(stationId)
    } catch (error) {
      console.error('Error getting voters by station:', error)
      throw error
    }
  })

  // Get voters by section
  ipcMain.handle('db:getVotersBySection', async (event, stationId: number, sectionId: number) => {
    try {
      return db.getVotersBySection(stationId, sectionId)
    } catch (error) {
      console.error('Error getting voters by section:', error)
      throw error
    }
  })

  // Get all polling stations
  ipcMain.handle('db:getAllPollingStations', async () => {
    try {
      return db.getAllPollingStations()
    } catch (error) {
      console.error('Error getting polling stations:', error)
      throw error
    }
  })

  // Get all sections
  ipcMain.handle('db:getAllSections', async () => {
    try {
      return db.getAllSections()
    } catch (error) {
      console.error('Error getting sections:', error)
      throw error
    }
  })

  // Import CSV data
  ipcMain.handle('db:importCSV', async (event, csvContent: string) => {
    try {
      // We'll need to implement CSV import in the main process
      const CSVImportService = require('../src/services/csvService').default
      const csvService = new CSVImportService()
      return await csvService.importFromCSV(csvContent)
    } catch (error) {
      console.error('Error importing CSV:', error)
      throw error
    }
  })

  // Get or create polling station
  ipcMain.handle('db:getOrCreatePollingStation', async (event, name: string) => {
    try {
      return db.getOrCreatePollingStation(name)
    } catch (error) {
      console.error('Error getting/creating polling station:', error)
      throw error
    }
  })

  // Get or create section
  ipcMain.handle('db:getOrCreateSection', async (event, name: string) => {
    try {
      return db.getOrCreateSection(name)
    } catch (error) {
      console.error('Error getting/creating section:', error)
      throw error
    }
  })
}