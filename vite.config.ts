import path from "path"
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
// import electron from 'vite-plugin-electron'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [
    tailwindcss(),
    react(),
    // electron([
    //   {
    //     entry: 'electron/main.ts',
    //   },
    // ]),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  base: './',
})